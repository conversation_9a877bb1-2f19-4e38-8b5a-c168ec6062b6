/**
 * Task Detail Component
 * 
 * Detailed view of a task with notes integration, template selection,
 * and comprehensive task information display.
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Stack,
  Chip,
  Divider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Edit as EditIcon,
  Notes as NotesIcon,
  Schedule as TimeIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';
import { Task } from '../../../types/task';
import { NoteTemplate, TaskNote } from '../../../types/notes';
import { NoteEditor, NotesList } from '../notes';
import { useNoteTemplates } from '../../../hooks/useNoteTemplates';
import { useTaskNotes } from '../../../hooks/useTaskNotes';
import { formatCurrency } from '../../../utils/formatters';

interface TaskDetailProps {
  task: Task;
  onBack: () => void;
  onEdit: (task: Task) => void;
  onDelete: (taskId: string) => void;
}

export function TaskDetail({ task, onBack, onEdit, onDelete }: TaskDetailProps) {
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');
  const [notesCollapsed, setNotesCollapsed] = useState(true);
  const [editingNote, setEditingNote] = useState<TaskNote | null>(null);
  const [deleteConfirm, setDeleteConfirm] = useState(false);

  // Hooks for templates and notes
  const { templates, getActiveTemplates } = useNoteTemplates();
  const {
    notes,
    createNote,
    updateNote,
    deleteNote,
    getNotesStats,
  } = useTaskNotes(task.id);

  const activeTemplates = getActiveTemplates();
  const selectedTemplate = templates.find(t => t.id === selectedTemplateId) || null;
  const notesStats = getNotesStats();

  // Auto-select first template if none selected and templates are available
  useEffect(() => {
    if (!selectedTemplateId && activeTemplates.length > 0) {
      setSelectedTemplateId(activeTemplates[0].id);
    }
  }, [selectedTemplateId, activeTemplates]);

  const handleTemplateChange = useCallback((templateId: string) => {
    if (editingNote) {
      // Show warning if there's an unsaved note
      const confirmChange = window.confirm(
        'Changing templates will discard any unsaved changes. Continue?'
      );
      if (!confirmChange) return;
      setEditingNote(null);
    }
    setSelectedTemplateId(templateId);
  }, [editingNote]);

  const handleSaveNote = useCallback(async (
    noteData: Omit<TaskNote, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<TaskNote> => {
    const savedNote = await createNote(noteData);
    setEditingNote(null);
    return savedNote;
  }, [createNote]);

  const handleUpdateNote = useCallback(async (
    noteId: string,
    updates: Partial<TaskNote>
  ): Promise<TaskNote> => {
    const updatedNote = await updateNote(noteId, updates);
    setEditingNote(null);
    return updatedNote;
  }, [updateNote]);

  const handleDeleteNote = useCallback(async (noteId: string): Promise<void> => {
    await deleteNote(noteId);
    if (editingNote?.id === noteId) {
      setEditingNote(null);
    }
  }, [deleteNote, editingNote]);

  const handleEditNote = useCallback((note: TaskNote) => {
    setEditingNote(note);
    setSelectedTemplateId(note.templateId);
    setNotesCollapsed(false);
  }, []);

  const handleCreateNote = useCallback(() => {
    setEditingNote(null);
    setNotesCollapsed(false);
  }, []);

  const handleDeleteTask = useCallback(() => {
    onDelete(task.id);
    setDeleteConfirm(false);
    onBack();
  }, [onDelete, task.id, onBack]);

  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }, []);

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Button
            startIcon={<BackIcon />}
            onClick={onBack}
            sx={{ mr: 2 }}
          >
            Back to Tasks
          </Button>
          <Typography variant="h4" sx={{ flex: 1 }}>
            {task.name}
          </Typography>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={() => onEdit(task)}
            sx={{ mr: 1 }}
          >
            Edit Task
          </Button>
          <Button
            variant="outlined"
            color="error"
            onClick={() => setDeleteConfirm(true)}
          >
            Delete Task
          </Button>
        </Box>

        {/* Task Info */}
        <Stack direction="row" spacing={3} sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <MoneyIcon color="action" />
            <Typography variant="body1">
              <strong>Hourly Rate:</strong> {
                task.hourlyRate ? formatCurrency(task.hourlyRate) : 'Not set'
              }
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TimeIcon color="action" />
            <Typography variant="body1">
              <strong>Created:</strong> {formatDate(task.createdAt)}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <NotesIcon color="action" />
            <Typography variant="body1">
              <strong>Notes:</strong> {notesStats.totalNotes}
            </Typography>
          </Box>
        </Stack>

        {/* Template Selection */}
        {activeTemplates.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <FormControl size="small" sx={{ minWidth: 200 }}>
              <InputLabel>Note Template</InputLabel>
              <Select
                value={selectedTemplateId}
                onChange={(e) => handleTemplateChange(e.target.value)}
                label="Note Template"
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {activeTemplates.map((template) => (
                  <MenuItem key={template.id} value={template.id}>
                    {template.name} ({template.fields.length} fields)
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        )}

        {activeTemplates.length === 0 && (
          <Alert severity="info" sx={{ mt: 2 }}>
            No note templates available. Create templates in the Note Templates section to start adding structured notes to this task.
          </Alert>
        )}
      </Paper>

      {/* Content Area */}
      <Box sx={{ flex: 1, display: 'flex', gap: 3, minHeight: 0 }}>
        {/* Left Panel - Notes Editor */}
        <Box sx={{ flex: 1 }}>
          <NoteEditor
            taskId={task.id}
            template={selectedTemplate}
            existingNote={editingNote}
            onSaveNote={handleSaveNote}
            onUpdateNote={handleUpdateNote}
            onDeleteNote={handleDeleteNote}
            isCollapsed={notesCollapsed}
            onToggleCollapse={() => setNotesCollapsed(!notesCollapsed)}
          />
        </Box>

        {/* Right Panel - Notes List */}
        <Box sx={{ width: 400, flexShrink: 0 }}>
          <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">Task Notes</Typography>
              {notesStats.templatesUsed.length > 0 && (
                <Box sx={{ mt: 1, display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                  {notesStats.templatesUsed.map((templateName) => (
                    <Chip
                      key={templateName}
                      label={`${templateName} (${notesStats.notesByTemplate[templateName]})`}
                      size="small"
                      variant="outlined"
                    />
                  ))}
                </Box>
              )}
            </Box>
            <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
              <NotesList
                taskId={task.id}
                notes={notes}
                onEditNote={handleEditNote}
                onDeleteNote={handleDeleteNote}
                onCreateNote={handleCreateNote}
              />
            </Box>
          </Paper>
        </Box>
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirm} onClose={() => setDeleteConfirm(false)}>
        <DialogTitle>Delete Task</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{task.name}"? This will also delete all associated notes and time entries. This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirm(false)}>Cancel</Button>
          <Button onClick={handleDeleteTask} color="error" variant="contained">
            Delete Task
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
